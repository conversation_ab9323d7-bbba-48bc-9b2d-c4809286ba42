import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../theme/app_theme.dart';

class VideoLessonScreen extends StatefulWidget {
  final String lessonTitle;
  final String courseTitle;
  final String instructor;
  final String duration;
  final int currentLesson;
  final int totalLessons;

  const VideoLessonScreen({
    super.key,
    required this.lessonTitle,
    required this.courseTitle,
    required this.instructor,
    required this.duration,
    required this.currentLesson,
    required this.totalLessons,
  });

  @override
  State<VideoLessonScreen> createState() => _VideoLessonScreenState();
}

class _VideoLessonScreenState extends State<VideoLessonScreen>
    with TickerProviderStateMixin {
  bool _isPlaying = false;
  bool _showControls = true;
  bool _isFullscreen = false;
  double _currentPosition = 0.3;
  late AnimationController _controlsAnimationController;
  late AnimationController _playButtonController;

  @override
  void initState() {
    super.initState();
    _controlsAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _playButtonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _startControlsTimer();
  }

  void _startControlsTimer() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _isPlaying) {
        setState(() {
          _showControls = false;
        });
        _controlsAnimationController.reverse();
      }
    });
  }

  void _togglePlayPause() {
    setState(() {
      _isPlaying = !_isPlaying;
      _showControls = true;
    });

    if (_isPlaying) {
      _playButtonController.forward();
      _startControlsTimer();
    } else {
      _playButtonController.reverse();
    }
    _controlsAnimationController.forward();
  }

  void _toggleFullscreen() {
    setState(() {
      _isFullscreen = !_isFullscreen;
    });

    if (_isFullscreen) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
      SystemChrome.setPreferredOrientations([
        DeviceOrientation.landscapeLeft,
        DeviceOrientation.landscapeRight,
      ]);
    } else {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    }
  }

  @override
  void dispose() {
    _controlsAnimationController.dispose();
    _playButtonController.dispose();
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isFullscreen) {
      return _buildFullscreenPlayer();
    }

    return Scaffold(
      backgroundColor: Theme.of(context).scaffoldBackgroundColor,
      body: SafeArea(
        child: Column(
          children: [
            _buildVideoPlayer(),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    _buildLessonInfo(),
                    _buildLessonsList(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVideoPlayer() {
    return Container(
      width: double.infinity,
      height: _isFullscreen ? MediaQuery.of(context).size.height : 220,
      color: Colors.black,
      child: Stack(
        children: [
          // Video placeholder with gradient
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppTheme.primaryPink.withOpacity(0.8),
                  AppTheme.accentBlue.withOpacity(0.8),
                ],
              ),
            ),
            child: Center(
              child: Icon(
                Icons.play_circle_filled,
                size: 80,
                color: Colors.white.withOpacity(0.9),
              ),
            ),
          ),

          // Video controls overlay
          if (_showControls)
            AnimatedBuilder(
              animation: _controlsAnimationController,
              builder: (context, child) {
                return Opacity(
                  opacity: _controlsAnimationController.value,
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withOpacity(0.7),
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ],
                      ),
                    ),
                    child: Column(
                      children: [
                        _buildTopControls(),
                        const Spacer(),
                        _buildCenterControls(),
                        const Spacer(),
                        _buildBottomControls(),
                      ],
                    ),
                  ),
                );
              },
            ),

          // Tap to show/hide controls
          GestureDetector(
            onTap: () {
              setState(() {
                _showControls = !_showControls;
              });
              if (_showControls) {
                _controlsAnimationController.forward();
                _startControlsTimer();
              } else {
                _controlsAnimationController.reverse();
              }
            },
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: Colors.transparent,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopControls() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: const Icon(Icons.arrow_back, color: Colors.white),
          ),
          const Spacer(),
          IconButton(
            onPressed: _toggleFullscreen,
            icon: Icon(
              _isFullscreen ? Icons.fullscreen_exit : Icons.fullscreen,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCenterControls() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        IconButton(
          onPressed: () {},
          icon: const Icon(Icons.replay_10, color: Colors.white, size: 32),
        ),
        GestureDetector(
          onTap: _togglePlayPause,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              _isPlaying ? Icons.pause : Icons.play_arrow,
              color: Colors.white,
              size: 40,
            ),
          ),
        ),
        IconButton(
          onPressed: () {},
          icon: const Icon(Icons.forward_10, color: Colors.white, size: 32),
        ),
      ],
    );
  }

  Widget _buildBottomControls() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                '${(_currentPosition * 100).toInt()}:30',
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: SliderTheme(
                  data: SliderTheme.of(context).copyWith(
                    activeTrackColor: AppTheme.primaryPink,
                    inactiveTrackColor: Colors.white.withOpacity(0.3),
                    thumbColor: AppTheme.primaryPink,
                    overlayColor: AppTheme.primaryPink.withOpacity(0.2),
                    trackHeight: 3,
                    thumbShape:
                        const RoundSliderThumbShape(enabledThumbRadius: 6),
                  ),
                  child: Slider(
                    value: _currentPosition,
                    onChanged: (value) {
                      setState(() {
                        _currentPosition = value;
                      });
                    },
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                widget.duration,
                style: const TextStyle(color: Colors.white, fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFullscreenPlayer() {
    return Scaffold(
      backgroundColor: Colors.black,
      body: _buildVideoPlayer(),
    );
  }

  Widget _buildLessonInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Lesson title
          Text(
            widget.lessonTitle,
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 8),

          // Course info
          Row(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppTheme.primaryPink.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  widget.courseTitle,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.primaryPink,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'by ${widget.instructor}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
          const SizedBox(height: 12),

          // Progress indicator
          Row(
            children: [
              Text(
                'Lesson ${widget.currentLesson} of ${widget.totalLessons}',
                style: Theme.of(context).textTheme.bodyMedium,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: LinearProgressIndicator(
                  value: widget.currentLesson / widget.totalLessons,
                  backgroundColor: AppTheme.primaryPink.withOpacity(0.2),
                  valueColor:
                      const AlwaysStoppedAnimation<Color>(AppTheme.primaryPink),
                ),
              ),
              const SizedBox(width: 12),
              Text(
                '${((widget.currentLesson / widget.totalLessons) * 100).toInt()}%',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.primaryPink,
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Added to favorites!')),
                    );
                  },
                  icon: const Icon(Icons.favorite_border, size: 18),
                  label: const Text('Save'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Shared!')),
                    );
                  },
                  icon: const Icon(Icons.share, size: 18),
                  label: const Text('Share'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLessonsList() {
    return Container(
      height: 400, // Fixed height to prevent overflow
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // Section header
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Text(
                  'Course Content',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppTheme.accentTeal.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    '${widget.totalLessons} lessons',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.accentTeal,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
              ],
            ),
          ),

          // Lessons list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              itemCount: widget.totalLessons,
              itemBuilder: (context, index) {
                final lessonNumber = index + 1;
                final isCurrentLesson = lessonNumber == widget.currentLesson;
                final isCompleted = lessonNumber < widget.currentLesson;

                return Container(
                  margin: const EdgeInsets.only(bottom: 8),
                  decoration: BoxDecoration(
                    color: isCurrentLesson
                        ? AppTheme.primaryPink.withOpacity(0.1)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(12),
                    border: isCurrentLesson
                        ? Border.all(
                            color: AppTheme.primaryPink.withOpacity(0.3))
                        : null,
                  ),
                  child: ListTile(
                    leading: Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: isCompleted
                            ? AppTheme.accentTeal
                            : isCurrentLesson
                                ? AppTheme.primaryPink
                                : AppTheme.textSecondary.withOpacity(0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        isCompleted ? Icons.check : Icons.play_arrow,
                        color: isCompleted || isCurrentLesson
                            ? Colors.white
                            : AppTheme.textSecondary,
                        size: 20,
                      ),
                    ),
                    title: Text(
                      'Lesson $lessonNumber: ${_getLessonTitle(lessonNumber)}',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            fontWeight: isCurrentLesson
                                ? FontWeight.w600
                                : FontWeight.normal,
                            color:
                                isCurrentLesson ? AppTheme.primaryPink : null,
                          ),
                    ),
                    subtitle: Text(
                      '${_getLessonDuration(lessonNumber)} • ${isCompleted ? 'Completed' : isCurrentLesson ? 'Current' : 'Not started'}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                    trailing: isCompleted
                        ? const Icon(Icons.check_circle,
                            color: AppTheme.accentTeal, size: 20)
                        : null,
                    onTap: () {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(content: Text('Opening lesson $lessonNumber')),
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  String _getLessonTitle(int lessonNumber) {
    final titles = [
      'Introduction to Flutter',
      'Setting up Development Environment',
      'Understanding Widgets',
      'Building Your First App',
      'State Management Basics',
      'Navigation and Routing',
      'Working with APIs',
      'Local Storage Solutions',
      'Testing Your App',
      'Publishing to App Stores',
    ];
    return titles[(lessonNumber - 1) % titles.length];
  }

  String _getLessonDuration(int lessonNumber) {
    final durations = [
      '12:30',
      '8:45',
      '15:20',
      '22:10',
      '18:35',
      '14:25',
      '20:15',
      '16:40',
      '11:55',
      '25:30'
    ];
    return durations[(lessonNumber - 1) % durations.length];
  }
}
