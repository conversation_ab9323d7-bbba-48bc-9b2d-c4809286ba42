import 'package:flutter/material.dart';
import 'main.dart';
import 'widgets/modern_background.dart';
import 'widgets/course_card.dart';
import 'theme/app_theme.dart';

class HomePage extends StatelessWidget {
  final String username;
  final VoidCallback onLogout; // Callback for logging out

  const HomePage({super.key, required this.username, required this.onLogout});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        title: const Text('Home'),
        actions: [
          IconButton(
            icon: const Icon(Icons.logout),
            tooltip: 'Logout',
            // Call the logout callback to lift the state change to MyApp.
            onPressed: onLogout,
          ),
        ],
        // A logout button is more appropriate than a back button here.
        automaticallyImplyLeading: false,
      ),
      extendBodyBehindAppBar: true,
      body: ModernBackground(
        child: CustomScrollView(
          slivers: [
            // Welcome header
            SliverToBoxAdapter(
              child: Container(
                padding: const EdgeInsets.fromLTRB(16, 100, 16, 24),
                child: <PERSON>umn(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome back,',
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                            color: AppTheme.textSecondary,
                          ),
                    ),
                    Text(
                      username,
                      style: Theme.of(context).textTheme.headlineLarge,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Continue your learning journey',
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
            ),

            // Featured courses section
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Row(
                  children: [
                    Text(
                      'Featured Courses',
                      style: Theme.of(context).textTheme.headlineSmall,
                    ),
                    const Spacer(),
                    TextButton(
                      onPressed: () {},
                      child: Text(
                        'See all',
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.primary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),

            // Course cards
            SliverList(
              delegate: SliverChildListDelegate([
                CourseCard(
                  title: 'Flutter Development Masterclass',
                  instructor: 'Angela Yu',
                  duration: '42h 30m',
                  rating: 4.8,
                  imageUrl: '',
                  accentColor: AppTheme.primaryPink,
                  onTap: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('Course tapped!')),
                    );
                  },
                ),
                CourseCard(
                  title: 'Advanced Dart Programming',
                  instructor: 'Maximilian Schwarzmüller',
                  duration: '28h 15m',
                  rating: 4.7,
                  imageUrl: '',
                  accentColor: AppTheme.accentBlue,
                ),
                CourseCard(
                  title: 'UI/UX Design Fundamentals',
                  instructor: 'Jonas Schmedtmann',
                  duration: '35h 45m',
                  rating: 4.9,
                  imageUrl: '',
                  accentColor: AppTheme.accentTeal,
                ),
                const SizedBox(height: 100), // Space for FAB
              ]),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          // Access the state of MyApp to get the theme setter function.
          final myAppState = context.findAncestorStateOfType<MyAppState>();
          if (myAppState == null) return;

          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                title: const Text('Select Theme'),
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    ListTile(
                      title: const Text('System Default'),
                      onTap: () {
                        myAppState.setThemeMode(ThemeMode.system);
                        Navigator.pop(context);
                      },
                    ),
                    ListTile(
                      title: const Text('Light Theme'),
                      onTap: () {
                        myAppState.setThemeMode(ThemeMode.light);
                        Navigator.pop(context);
                      },
                    ),
                    ListTile(
                      title: const Text('Dark Theme'),
                      onTap: () {
                        myAppState.setThemeMode(ThemeMode.dark);
                        Navigator.pop(context);
                      },
                    ),
                  ],
                ),
              );
            },
          );
        },
        child: const Icon(Icons.brightness_4), // Icon for theme selection
      ),
    );
  }
}
